import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { FlowModel } from '@core/models';
import { FlowProcess } from './flow-process.entity';

// TODO: add project

@Entity('flows')
export class Flow implements UnknownProperties<FlowModel> {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'integer', nullable: true })
  prevId: number | null;

  @OneToMany(() => FlowProcess, process => process.flow)
  processes: FlowProcess[];

  // @Column({ type: 'integer', nullable: true })
  // parentId: EntityId | null;

  // // TODO: do we really need these fields
  // @CreateDateColumn({ name: 'created_at' })
  // createdAt: Date;

  // @UpdateDateColumn({ name: 'updated_at' })
  // updatedAt: Date;
}
