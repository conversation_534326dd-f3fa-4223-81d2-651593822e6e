import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Logger } from 'nestjs-pino';
import { LoggerOptions } from 'typeorm';
import { isDevelopment } from '@libs/common/envs';
import { PinoTypeOrmLogger } from '@libs/common/logger';
import { EnvironmentVariables } from '../config';
import { DatabaseService } from './database.service';
import { Flow, FlowProcess, FlowProcessStep } from './flows';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      inject: [ConfigService, Logger],
      useFactory: (config: ConfigService<EnvironmentVariables>, logger: Logger) => ({
        type: 'postgres',
        host: config.get('PG_HOST'),
        port: config.get('PG_PORT'),
        password: config.get('PG_PASSWORD'),
        username: config.get('PG_USER'),
        database: config.get('PG_DB'),
        // entities: [Flow, FlowProcess, FlowProcessStep],
        autoLoadEntities: true,
        // synchronize: true,
        logging: (isDevelopment() ? 'all' : 'info') as LoggerOptions,
        logger: new PinoTypeOrmLogger(logger),
      }),
    }),

    TypeOrmModule.forFeature([Flow, FlowProcess, FlowProcessStep]),
  ],

  providers: [DatabaseService],
  exports: [DatabaseService],
})
export class DatabaseModule {}
