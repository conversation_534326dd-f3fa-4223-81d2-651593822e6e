import { z } from 'zod';

export const FlowProcessStepSchema = z.object({
  id: z.coerce.string().describe('Step id'),
  name: z.string().describe('Step name'),
  description: z.string().nullable().describe('Step description'),
  parentId: z.coerce.string().nullable().describe('Step parent id'),
  prevId: z.coerce.string().nullable().describe('Step previous id'),
  mPath: z.string().describe('Step materialized path'),
});

export type FlowProcessStepModel = z.infer<typeof FlowProcessStepSchema>;
