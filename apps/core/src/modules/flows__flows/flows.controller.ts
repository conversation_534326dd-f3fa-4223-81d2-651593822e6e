import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Put } from '@nestjs/common';
import { ApiParam } from '@nestjs/swagger';
import { z } from 'zod';
import { FlowSchema } from '@core/models';
import { ApiError, ApiSuccessfulResponse, HttpResponse, SanitizeResponseWithZod } from '@libs/common/api';
import { CreateFlowDto, FlowDto, MoveFlowDto, PartialUpdateFlowDto, UpdateFlowDto } from './flows.dto';
import {
  CreateFlowUseCase,
  DeleteFlowByIdUseCase,
  GetAllFlowsUseCase,
  GetFlowByIdUseCase,
  MoveFlowUseCase,
  UpdateFlowUseCase,
} from './use-cases';

const ApiParamFlowId = () =>
  ApiParam({ name: 'flowId', required: true, description: 'Flow identifier', type: 'string' });

@Controller('flows')
export class FlowsController {
  constructor(
    private readonly createFlowUseCase: CreateFlowUseCase,
    private readonly updateFlowUseCase: UpdateFlowUseCase,
    private readonly deleteFlowByIdUseCase: DeleteFlowByIdUseCase,
    private readonly moveFlowUseCase: MoveFlowUseCase,
    private readonly getFlowByIdUseCase: GetFlowByIdUseCase,
    private readonly getAllFlowsUseCase: GetAllFlowsUseCase,
  ) {}

  @Post()
  @ApiSuccessfulResponse(HttpStatus.CREATED, 'Flow created', FlowDto)
  @SanitizeResponseWithZod(FlowSchema)
  async createFlow(@Body() dto: CreateFlowDto) {
    const flow = await this.createFlowUseCase.execute({ dto });
    return new HttpResponse({ statusCode: HttpStatus.CREATED, data: flow, message: 'Flow created' });
  }

  @Put(':flowId')
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow updated', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(FlowSchema)
  async updateFlow(@Param('flowId') flowId: string, @Body() dto: UpdateFlowDto) {
    const flow = await this.updateFlowUseCase.execute({ flowId, dto });
    return new HttpResponse({ data: flow, message: 'Flow updated' });
  }

  @Patch(':flowId')
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow updated', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(FlowSchema)
  async partialUpdateFlow(@Param('flowId') flowId: string, @Body() dto: PartialUpdateFlowDto) {
    const flow = await this.updateFlowUseCase.execute({ flowId, dto });
    return new HttpResponse({ data: flow, message: 'Flow updated' });
  }

  @Delete(':flowId')
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow deleted', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(FlowSchema)
  async deleteFlow(@Param('flowId') flowId: string) {
    const flow = await this.deleteFlowByIdUseCase.execute({ flowId });
    return new HttpResponse({ data: flow, message: 'Flow deleted' });
  }

  @Post(':flowId/move')
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow moved', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(FlowSchema)
  async moveFlow(@Param('flowId') flowId: string, @Body() dto: MoveFlowDto) {
    const flow = await this.moveFlowUseCase.execute({ flowId, dto });
    return new HttpResponse({ data: flow, message: 'Flow moved' });
  }

  @Get(':flowId')
  @SanitizeResponseWithZod(FlowSchema)
  @ApiParamFlowId()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flow found', FlowDto)
  @ApiError(HttpStatus.NOT_FOUND, 'Flow not found', 'FLOW_NOT_FOUND')
  @SanitizeResponseWithZod(FlowSchema)
  async getFlow(@Param('flowId') flowId: string) {
    const flow = await this.getFlowByIdUseCase.execute({ flowId });
    return new HttpResponse({ data: flow, message: 'Flow found' });
  }

  @Get()
  @ApiSuccessfulResponse(HttpStatus.OK, 'Flows found', [FlowDto])
  @SanitizeResponseWithZod(z.array(FlowSchema))
  async getAllFlows() {
    const flows = await this.getAllFlowsUseCase.execute();
    return new HttpResponse({ data: flows, message: 'Flows found' });
  }
}
