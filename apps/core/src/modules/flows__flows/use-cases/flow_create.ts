import { Injectable, Logger } from '@nestjs/common';
import { IsNull, Not } from 'typeorm';
import { DatabaseService, Flow } from '@core/database';
import { CreateFlowDto } from '../flows.dto';
import { FlowsService } from '../flows.service';

@Injectable()
export class CreateFlowUseCase implements UseCase {
  private readonly logger = new Logger(CreateFlowUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly flowsService: FlowsService,
  ) {}

  async execute({ dto }: { dto: CreateFlowDto }): Promise<Flow> {
    const { prevId } = dto;

    this.logger.verbose({ msg: 'Started creating flow', data: { dto } });

    const [flow, flowWithSamePrevId] = await this.db.flows.manager.transaction(
      // 'READ COMMITTED',
      async entityManager => {
        /** Create flow entity */
        const flow = entityManager.create(Flow, this.flowsService.normalizeEntityValues(new Flow(), dto));

        this.logger.verbose({ msg: 'Flow entity created', data: flow });

        await entityManager.save(flow);

        this.logger.verbose({ msg: 'Flow saved', data: flow });

        this.logger.verbose({ msg: 'Finding flow with same prevId', data: { prevId } });

        /** Find flow with same prevId */
        const flowWithSamePrevId = await entityManager.findOne(Flow, {
          where: {
            id: Not(flow.id),
            prevId: prevId === null ? IsNull() : flow.id,
          },
        });

        /** Shift flowWithSamePrevId below if it exists */
        if (flowWithSamePrevId) {
          this.logger.verbose({ msg: 'Found flow with same prevId', data: flowWithSamePrevId });

          this.logger.verbose({
            msg: 'Updating prevId for existin flow with same prevId',
            data: { flowId: flowWithSamePrevId.id, prevId: flow.id },
          });

          flowWithSamePrevId.prevId = flow.id;
          await entityManager.save(flowWithSamePrevId);
        } else {
          this.logger.verbose({ msg: 'No flow with same prevId found' });
        }

        return [flow, flowWithSamePrevId];
      },
    );

    this.logger.log({
      msg: 'Flow created',
      data: {
        incomingData: { dto },
        createdFlow: flow,
        modifiedData: { flowWithSamePrevId },
      },
    });

    return flow;
  }
}
