import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, Flow } from '@core/database';
import { pgId } from '@libs/common/database';
import { UpdateFlowDto, PartialUpdateFlowDto } from '../flows.dto';
import { FlowsService } from '../flows.service';

@Injectable()
export class UpdateFlowUseCase implements UseCase {
  private readonly logger = new Logger(UpdateFlowUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly flowsService: FlowsService,
  ) {}

  async execute({
    flowId,
    dto,
  }: {
    flowId: string;
    dto: UpdateFlowDto | PartialUpdateFlowDto;
  }): Promise<Flow> {
    const { name, description } = dto;
    this.logger.verbose({ msg: 'Started updating flow', data: { flowId, dto } });

    this.logger.verbose({ msg: 'Finding flow to update', data: { flowId } });

    const flow = await this.db.flows.findOne({
      where: { id: pgId(flowId) },
    });

    if (!flow) throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');

    this.logger.verbose({ msg: 'Flow found', data: flow });

    this.logger.verbose({ msg: 'Updating flow', flow });

    const modifiedFlow = this.flowsService.normalizeEntityValues(flow, {
      name,
      description,
    });

    await this.db.flows.save(modifiedFlow);

    this.logger.log({
      msg: 'Flow updated',
      data: {
        incomingData: { flowId, dto },
        modifiedProcess: modifiedFlow,
      },
    });

    return modifiedFlow;
  }
}
