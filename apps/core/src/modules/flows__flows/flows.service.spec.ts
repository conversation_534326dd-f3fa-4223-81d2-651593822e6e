import { Test, TestingModule } from '@nestjs/testing';
import { Flow } from '@core/database';
import { FlowsService } from './flows.service';

describe('FlowsService', () => {
  let service: FlowsService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FlowsService],
    }).compile();

    service = module.get(FlowsService);
  });

  describe('normalizeEntityValues', () => {
    it('should set description to null if it is an empty string', () => {
      const flow = new Flow();
      flow.description = '';
      const normalizedFlow = service.normalizeEntityValues(flow, {});
      expect(normalizedFlow.description).toBeNull();
    });
  });
});
