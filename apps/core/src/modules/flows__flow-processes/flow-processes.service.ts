import { Injectable, Logger } from '@nestjs/common';
import { customAlphabet } from 'nanoid';
import { EntityManager } from 'typeorm';
import { FlowProcess } from '@core/database';
import { FlowProcessModel } from '@core/models';
import { pgId } from '@libs/common/database';

const alphabet = '0123456789abcdefghijklmnopqrstuvwxyz';
const nanoid = customAlphabet(alphabet, 6);

@Injectable()
export class FlowProcessesService {
  private readonly logger = new Logger(FlowProcessesService.name);

  constructor() {}

  public normalizeEntityValues(
    entity: FlowProcess,
    values: Partial<FlowProcess | FlowProcessModel>,
  ): FlowProcess {
    Object.assign(entity, values);
    if (entity.description === '') entity.description = null;
    return entity;
  }

  private generateMaterializedPath(parentMPath?: string): string {
    const path = nanoid();
    return parentMPath ? parentMPath + '/' + path : path;
  }

  /**
   * Potential issue with recursion.
   */
  public async generateUniqueMaterializedPath(
    entityManager: EntityManager,
    params: {
      flowId: string;
      parentMPath?: string;
    },
  ): Promise<string> {
    const { flowId, parentMPath } = params;
    this.logger.verbose({ msg: 'Generating mPath (recursively)', data: params });

    const mPath = this.generateMaterializedPath(parentMPath);

    this.logger.verbose({ msg: 'Checking if mPath already exists', data: { mPath } });

    const existingProcessWithSamePath = await entityManager.findOne(FlowProcess, {
      where: {
        flow: { id: pgId(flowId) },
        mPath,
      },
    });

    if (existingProcessWithSamePath) {
      this.logger.warn({ msg: 'mPath already exists, generating new mPath', data: { mPath } });
      return await this.generateUniqueMaterializedPath(entityManager, { flowId: flowId, parentMPath });
    } else {
      this.logger.verbose({ msg: 'mPath is unique', data: { mPath } });
    }

    return mPath;
  }
}
