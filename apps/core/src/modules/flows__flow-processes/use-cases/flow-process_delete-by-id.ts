import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Like } from 'typeorm';
import { DatabaseService, FlowProcess } from '@core/database';
import { pgId } from '@libs/common/database';

@Injectable()
export class DeleteFlowProcessByIdUseCase implements UseCase {
  private readonly logger = new Logger(DeleteFlowProcessByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  async execute({ processId, flowId }: { processId: string; flowId: string }): Promise<FlowProcess> {
    this.logger.verbose({ msg: 'Started deleting process', data: { processId, flowId } });

    const [deletedProcess, nextProcess] = await this.db.flowProcesses.manager.transaction(
      async entityManager => {
        this.logger.verbose({ msg: 'Finding process to delete', data: { processId } });

        /** Find process to delete */
        const processToDelete = await entityManager.findOne(FlowProcess, {
          where: { id: pgId(processId) },
        });

        if (!processToDelete) throw new NotFoundException('Process not found', 'PROCESS_NOT_FOUND');

        this.logger.verbose({ msg: 'Process found', data: processToDelete });

        this.logger.verbose({ msg: 'Deleting process', data: { processId: processToDelete.id } });

        /** Delete process */
        await entityManager.delete(FlowProcess, processToDelete.id);

        this.logger.verbose({ msg: 'Process deleted', data: { processId: processToDelete.id } });

        this.logger.verbose({ msg: 'Deleting children (mPath/%)', data: { mPath: processToDelete.mPath } });

        /** Delete children */
        await entityManager.delete(FlowProcess, {
          flow: { id: pgId(flowId) },
          mPath: Like(`${processToDelete.mPath}/%`),
        });

        this.logger.verbose({ msg: 'Children deleted', data: { mPath: processToDelete.mPath } });

        this.logger.verbose({
          msg: 'Finding next process to update prevId',
          data: { prevId: processToDelete.id },
        });

        /** Update next process prevId */
        const nextProcess = await entityManager.findOne(FlowProcess, {
          where: {
            flow: { id: pgId(flowId) },
            prevId: processToDelete.id,
          },
        });

        if (nextProcess) {
          this.logger.verbose({ msg: 'Next process found', data: nextProcess });

          this.logger.verbose({
            msg: 'Updating next process prevId',
            data: { prevId: processToDelete.prevId },
          });

          nextProcess.prevId = processToDelete.prevId;
          await entityManager.save(FlowProcess, nextProcess);
        } else {
          this.logger.verbose({ msg: 'No next process found' });
        }

        return [processToDelete, nextProcess];
      },
    );

    this.logger.log({
      msg: 'Process deleted with its children',
      data: {
        incomingData: { processId, flowId },
        deletedProcess,
        modifiedData: { nextProcess },
      },
    });

    return deletedProcess;
  }
}
