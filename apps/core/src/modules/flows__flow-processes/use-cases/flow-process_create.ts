import { ConflictException, Injectable, Logger } from '@nestjs/common';
import { IsNull, Not } from 'typeorm';
import { DatabaseService, FlowProcess } from '@core/database';
import { pgId } from '@libs/common/database';
import { CreateFlowProcessDto } from '../flow-processes.dto';
import { FlowProcessesService } from '../flow-processes.service';

@Injectable()
export class CreateFlowProcessUseCase implements UseCase {
  private readonly logger = new Logger(CreateFlowProcessUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly processesService: FlowProcessesService,
  ) {}

  async execute({ flowId, dto }: { flowId: string; dto: CreateFlowProcessDto }): Promise<FlowProcess> {
    const { parentId, prevId } = dto;

    this.logger.verbose({ msg: 'Started creating process', data: { flowId, dto } });

    const [process, processWithSamePrevId] = await this.db.flowProcesses.manager.transaction(
      // 'READ COMMITTED',
      async entityManager => {
        let parentMPath: string | undefined = undefined;

        if (parentId) {
          this.logger.verbose({ msg: 'Finding parent process', data: { parentId } });

          const parent = await entityManager.findOne(FlowProcess, {
            where: { id: pgId(parentId) },
          });
          if (!parent) throw new ConflictException('Parent process not found', 'PARENT_PROCESS_NOT_FOUND');

          this.logger.verbose({ msg: 'Parent process found', data: parent });

          parentMPath = parent.mPath;
        }

        this.logger.verbose({ msg: 'Generating mPath' });

        /** Generate mPath */
        const mPath = await this.processesService.generateUniqueMaterializedPath(entityManager, {
          flowId,
          parentMPath,
        });

        this.logger.verbose({ msg: 'Generated mPath', data: { mPath } });

        /** Create process entity */
        const process = entityManager.create(FlowProcess, {
          ...this.processesService.normalizeEntityValues(new FlowProcess(), { ...dto, mPath }),
          flow: { id: pgId(flowId) },
        });

        this.logger.verbose({ msg: 'Process entity created', data: process });

        await entityManager.save(process);

        this.logger.verbose({ msg: 'Process saved', data: process });

        this.logger.verbose({ msg: 'Finding process with same prevId', data: { prevId } });

        /** Find process with same prevId */
        const processWithSamePrevId = await entityManager.findOne(FlowProcess, {
          where: {
            id: Not(process.id),
            flow: { id: pgId(flowId) },
            prevId: prevId === null ? IsNull() : process.id,
          },
        });

        /** Shift processWithSamePrevId below if it exists */
        if (processWithSamePrevId) {
          this.logger.verbose({ msg: 'Found process with same prevId', data: processWithSamePrevId });

          this.logger.verbose({
            msg: 'Updating prevId for existin process with same prevId',
            data: { processId: processWithSamePrevId.id, prevId: process.id },
          });

          processWithSamePrevId.prevId = process.id;
          await entityManager.save(processWithSamePrevId);
        } else {
          this.logger.verbose({ msg: 'No process with same prevId found' });
        }

        return [process, processWithSamePrevId];
      },
    );

    this.logger.log({
      msg: 'Process created',
      data: {
        incomingData: { flowId, dto },
        createdProcess: process,
        modifiedData: { processWithSamePrevId },
      },
    });

    return process;
  }
}
