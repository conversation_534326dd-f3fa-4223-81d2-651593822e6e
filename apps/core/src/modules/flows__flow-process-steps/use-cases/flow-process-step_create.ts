import { ConflictException, Injectable, Logger } from '@nestjs/common';
import { IsNull, Not } from 'typeorm';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { pgId } from '@libs/common/database';
import { CreateFlowProcessStepDto } from '../flow-process-steps.dto';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class CreateFlowProcessStepUseCase implements UseCase {
  private readonly logger = new Logger(CreateFlowProcessStepUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly stepsService: FlowProcessStepsService,
  ) {}

  async execute({
    processId,
    dto,
  }: {
    processId: string;
    dto: CreateFlowProcessStepDto;
  }): Promise<FlowProcessStep> {
    const { parentId, prevId } = dto;

    this.logger.verbose({ msg: 'Started creating step', data: { processId, dto } });

    const [step, stepWithSamePrevId] = await this.db.flowProcessSteps.manager.transaction(
      // 'READ COMMITTED',
      async entityManager => {
        let parentMPath: string | undefined = undefined;

        if (parentId) {
          this.logger.verbose({ msg: 'Finding parent step', data: { parentId } });

          const parent = await entityManager.findOne(FlowProcessStep, {
            where: { id: pgId(parentId) },
          });
          if (!parent) throw new ConflictException('Parent step not found', 'PARENT_STEP_NOT_FOUND');

          this.logger.verbose({ msg: 'Parent step found', data: parent });

          parentMPath = parent.mPath;
        }

        this.logger.verbose({ msg: 'Generating mPath' });

        /** Generate mPath */
        const mPath = await this.stepsService.generateUniqueMaterializedPath(entityManager, {
          processId,
          parentMPath,
        });

        this.logger.verbose({ msg: 'Generated mPath', data: { mPath } });

        /** Create step entity */
        const step = entityManager.create(FlowProcessStep, {
          ...this.stepsService.normalizeEntityValues(new FlowProcessStep(), { ...dto, mPath }),
          flowProcess: { id: pgId(processId) },
        });

        this.logger.verbose({ msg: 'Step entity created', data: step });

        await entityManager.save(step);

        this.logger.verbose({ msg: 'Step saved', data: step });

        this.logger.verbose({ msg: 'Finding step with same prevId', data: { prevId } });

        /** Find step with same prevId */
        const stepWithSamePrevId = await entityManager.findOne(FlowProcessStep, {
          where: {
            id: Not(step.id),
            flowProcess: { id: pgId(processId) },
            prevId: prevId === null ? IsNull() : step.id,
          },
        });

        /** Shift stepWithSamePrevId below if it exists */
        if (stepWithSamePrevId) {
          this.logger.verbose({ msg: 'Found step with same prevId', data: stepWithSamePrevId });

          this.logger.verbose({
            msg: 'Updating prevId for existing step with same prevId',
            data: { stepId: stepWithSamePrevId.id, prevId: step.id },
          });

          stepWithSamePrevId.prevId = step.id;
          await entityManager.save(stepWithSamePrevId);
        } else {
          this.logger.verbose({ msg: 'No step with same prevId found' });
        }

        return [step, stepWithSamePrevId];
      },
    );

    this.logger.log({
      msg: 'Step created',
      data: {
        incomingData: { processId, dto },
        createdStep: step,
        modifiedData: { stepWithSamePrevId },
      },
    });

    return step;
  }
}
