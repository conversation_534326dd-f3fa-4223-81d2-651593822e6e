import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, FlowProcessStep } from '@core/database';
import { pgId } from '@libs/common/database';
import { PartialUpdateFlowProcessStepDto, UpdateFlowProcessStepDto } from '../flow-process-steps.dto';
import { FlowProcessStepsService } from '../flow-process-steps.service';

@Injectable()
export class UpdateFlowProcessStepUseCase implements UseCase {
  private readonly logger = new Logger(UpdateFlowProcessStepUseCase.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly stepsService: FlowProcessStepsService,
  ) {}

  async execute({
    stepId,
    dto,
  }: {
    stepId: string;
    dto: UpdateFlowProcessStepDto | PartialUpdateFlowProcessStepDto;
  }): Promise<FlowProcessStep> {
    const { name, description } = dto;
    this.logger.verbose({ msg: 'Started updating step', data: { stepId, dto } });

    this.logger.verbose({ msg: 'Finding step to update', data: { stepId } });

    const step = await this.db.flowProcessSteps.findOne({
      where: { id: pgId(stepId) },
    });

    if (!step) throw new NotFoundException('Step not found', 'STEP_NOT_FOUND');

    this.logger.verbose({ msg: 'Step found', data: step });

    this.logger.verbose({ msg: 'Updating step', stepId });

    const modifiedStep = this.stepsService.normalizeEntityValues(step, {
      name,
      description,
    });

    await this.db.flowProcessSteps.save(modifiedStep);

    this.logger.log({
      msg: 'Step updated',
      data: {
        incomingData: { stepId, dto },
        modifiedStep,
      },
    });

    return modifiedStep;
  }
}
