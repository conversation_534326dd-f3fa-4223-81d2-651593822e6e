import { createZodDto } from 'nestjs-zod';
import { FlowProcessStepSchema } from '@core/models';

export class FlowProcessStepDto extends createZodDto(FlowProcessStepSchema) {}

const CreateFlowProcessStepSchema = FlowProcessStepSchema.pick({
  name: true,
  description: true,
  parentId: true,
  prevId: true,
});

export class CreateFlowProcessStepDto extends createZodDto(CreateFlowProcessStepSchema) {}

const UpdateFlowProcessStepSchema = FlowProcessStepSchema.pick({
  name: true,
  description: true,
});

export class UpdateFlowProcessStepDto extends createZodDto(UpdateFlowProcessStepSchema) {}

const PartialUpdateFlowProcessStepSchema = UpdateFlowProcessStepSchema.partial();

export class PartialUpdateFlowProcessStepDto extends createZodDto(PartialUpdateFlowProcessStepSchema) {}

const MoveFlowProcessStepSchema = FlowProcessStepSchema.pick({
  parentId: true,
  prevId: true,
});

export class Move<PERSON>lowProcessStepDto extends createZodDto(MoveFlowProcessStepSchema) {}
