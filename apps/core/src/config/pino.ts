import { ConfigService } from '@nestjs/config';
import { Params } from 'nestjs-pino';
import { randomUUID } from 'node:crypto';
import { ecsFormat } from '@elastic/ecs-pino-format';
import { isDevelopment } from '@libs/common/envs';
import { EnvironmentVariables } from './env-variables';

export const getPinoHttpOptions = (config: ConfigService<EnvironmentVariables>): Params['pinoHttp'] => {
  const baseOptions: Params['pinoHttp'] = {
    level: config.get('LOG_LEVEL'),
    genReqId: request => request.headers['x-correlation-id'] || randomUUID(),
  };

  // Use pino-http-send if Logstash URL is provided
  if (config.get<string>('LOGSTASH_HOST')) {
    return {
      ...baseOptions,
      transport: {
        target: 'pino-socket',
        options: {
          address: config.get<string>('LOGSTASH_HOST'),
          port: config.get<number>('LOGSTASH_PORT'),
          mode: 'tcp',
        },
      },
      ...ecsFormat({
        convertReqRes: true,
        apmIntegration: true,
        serviceName: 'easy-flow-core',
      }),
      messageKey: 'msg',
      quietReqLogger: true,
      quietResLogger: true,
    };
  }

  // Use pino-pretty for development
  if (isDevelopment()) {
    return {
      ...baseOptions,
      transport: {
        target: 'pino-pretty',
        options: {
          translateTime: 'SYS:standard',
          colorize: true,
          singleLine: true,
          ignore:
            'pid,hostname,context,req.headers,req.params,req.query,req.remoteAddress,req.remotePort,res',
        },
      },
    };
  }

  return baseOptions;
};
