import { Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ZodValidationPipe } from 'nestjs-zod';
import { GlobalApiExceptionFilter, ZodResponseInterceptor } from '@libs/common/api';
import { EnvsModule, includeAppEnvFilePaths } from '@libs/common/envs';
import { PinoLoggerModule } from '@libs/common/logger';
import { EnvVariablesSchema } from './config';
import { FlowProcessStepsModule } from './modules/flows__flow-process-steps/flow-process-steps.module';
import { FlowProcessesModule } from './modules/flows__flow-processes/flow-processes.module';
import { FlowsModule } from './modules/flows__flows/flows.module';

@Module({
  imports: [
    EnvsModule.forRoot({
      schema: EnvVariablesSchema,
      envFilePath: includeAppEnvFilePaths('core'),
    }),

    PinoLoggerModule.forRootAsync('easy-flow-core'),

    FlowsModule,
    FlowProcessesModule,
    FlowProcessStepsModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ZodResponseInterceptor,
    },
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalApiExceptionFilter,
    },
  ],
})
export class CoreModule {}
