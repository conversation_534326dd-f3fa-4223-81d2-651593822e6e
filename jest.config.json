{"verbose": true, "testEnvironment": "node", "moduleFileExtensions": ["js", "json", "ts"], "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "rootDir": ".", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@libs/common(|/.*)$": "<rootDir>/libs/common/src/$1", "^@core(|/.*)$": "<rootDir>/apps/core/src/$1"}}