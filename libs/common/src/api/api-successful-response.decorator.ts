import { applyDecorators, HttpStatus, mixin } from '@nestjs/common';
import { ApiProperty, ApiResponse } from '@nestjs/swagger';
import { SchemaObjectMetadata } from '@nestjs/swagger/dist/interfaces/schema-object-metadata.interface';

// https://www.inextenso.dev/how-to-generate-generic-dtos-with-nestjs-and-swagger

type Schema = {
  properties: Record<string, SchemaObjectMetadata>;
  required?: string[];
};

export function ApiSuccessfulResponse(
  statusCode: HttpStatus,
  message: string | undefined,
  data?: Class | Schema | Class[],
  // options?: ApiResponseOptions,
) {
  const dataType = typeof data;

  const isModel = dataType === 'function';
  const isObject = dataType === 'object';
  const isArray = Array.isArray(data);

  const MessageDecorator = message ? ApiProperty({ example: message }) : () => {};

  const DataDecorator = isArray
    ? ApiProperty({ type: () => data[0], isArray: true })
    : isModel
      ? ApiProperty({ type: data as Class })
      : isObject
        ? ApiProperty({ type: 'object', selfRequired: true, ...(data as Schema) })
        : () => {};

  // based on HttpResponse
  class ApiSuccessfulResponseDto {
    @ApiProperty({ example: true })
    success: true;

    @ApiProperty({ enum: HttpStatus, example: statusCode })
    statusCode: HttpStatus;

    @MessageDecorator
    message?: string;

    @DataDecorator
    // @Type(() => data)
    // @ValidateNested()
    data: unknown;
  }

  const OutputModel = mixin(ApiSuccessfulResponseDto);

  return applyDecorators(
    ApiResponse({
      status: statusCode,
      type: OutputModel,
    }),
  );
}
